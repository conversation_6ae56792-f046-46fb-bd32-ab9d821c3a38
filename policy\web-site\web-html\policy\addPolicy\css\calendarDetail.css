body,html{
    background: url(../union/images/detailBgMin.png) repeat;
    min-width: 1400px;
}
.bgBox{
    background-image: url(../union/images/detailBgTop.png),url(../union/images/detailBgBottom.png);
    background-repeat: no-repeat, no-repeat;
    background-position: top left, bottom right;
    padding-bottom: 40px;
    min-width: 1400px;
}
.leftBox .logo{
    width: 58px;
    height: 24px;
    line-height: 24px;
    padding: 0px 12px 0px 4px;
    font-size: 14px;
    color: #ffffff;
    background-size: 100% 100%;
    box-sizing: border-box;
    margin-right: 5px;
}
.logo1{
    color: #fff;
    background: url(../images/sqz.png) no-repeat center center;
}
.logo2{
    background: url(../images/ps_box2Icon2.png) no-repeat center;
    color: #fff;
}
.logo3{
    background: url(../images/ps_box2Icon3.png) no-repeat center;
    color: #fff;
}
/* 左侧 */
.leftBox {
    width: 1050px;
}

.leftBox .left_topBox {
    background: url(../images/leftBg.png) no-repeat center center;
    padding: 28px 29px;
    border-radius: 8px;
    box-shadow: 0px 0px 10px rgba(20, 82, 161, .26);
}
.leftBox .titleBox h2{
    font-size: 20px;
    color: #303946;
    font-weight: bold;
    width: 757px;
    line-height: 24px;
}
.leftBox .icon01{
    padding-left: 20px;
    background: url(.././images/icons/time.png) no-repeat left center;
    color: #8b94a1;
    padding-right: 30px;
}
.leftBox .icon02{
    padding-left: 20px;
    background: url(.././images/icons/view.png) no-repeat left center;
    color: #8b94a1;
}
.leftBox .infoBox{
    width: 818px;
    font-size: 16px;
}
.leftBox .infoBox p{
    color: #8b94a1;
    line-height: 24px;
    padding: 6px 0;
}
.w290{
    /* width: 290px; */
    width: 710px;
}
.w190{
    /* width: 190px; */
    width: 138px;
}
.w90{
    width: 90px;
}
.contentBox .hd {
    height: 68px;
    background: #fff;
    border-radius: 5px;
    box-shadow: 0 3px 10px #e0e8f2;
    padding: 0 0 0 4px;
    margin-top: 34px;
}

.contentBox .hd li {
    width: 181px;
    height: 68px;
    line-height: 70px;
    text-align: center;
    font-size: 18px;
    color: #666;
    cursor: pointer;
    margin-right: 20px;
    border-radius: 34px;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}

.contentBox .hd li:hover {
    background: #e6f0ff;
    color: #0052d9;
}

.contentBox .hd li.on {
    background: #0052d9;
    color: #fff;
}

.contentBox .hd li:first-child.on,
.contentBox .hd li:first-child:hover {
    background: #0052d9;
    color: #fff;
}
.contentBox .bdBox,
.contentBox .original{
    border: 1px solid #e4f1fc;
    background: #fff;
    border-radius: 8px;
    margin-top: 15px;
    padding: 0px 25px;
    margin-bottom: 30px;
    min-height: 300px;
    color: #5e6774;
    line-height: 28px;
    /* padding: 8px 0; */
}
.contentBox .original img{
    max-width: 100%;
    display: block;
    margin: 5px auto;
}
.conTitle{
    background: url(../images/delTitIcon.png) no-repeat 14px center;    
    background-color: #ebf2fe;
    height: 50px;
    border-radius: 8px;
    line-height: 50px;
    padding-left: 38px;
    color: #0052d9;
    font-weight: bold;
    font-size: 18px;
    margin: 27px 0 17px;
}
.videoPop,.audioPop{
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url(.././images/icons/popUp_bg.png) repeat;
    z-index: 10000;
}
.mianvideo{
    margin: 10% auto 0;
    width: 750px;
    height: 400px;
}
.mianvideo a,.mianaudio a{
    display: block;
    width: 15px;
    height: 15px;
    top: -25px;
    right: -20px;
    background: url(.././images/icons/close3.png) no-repeat;
}
.mianaudio{
    width: 300px;
    height: 60px;
    margin: 10% auto 0;
}
.myVideo{
    width: 750px;
    height: 400px;
    float: left;
    margin: 0 20px 20px 0;
    background: #1a1a1a;
}
.playBtn{
    display: block;
    margin-bottom: 10px;
    color: #1569e6;
}
.playBtn:hover{
    color: #1569e6;
}
.audio{
    float: left;
    margin: 0 20px 20px 0;
}
.contentBox .picBox {
    padding-bottom: 20px;
}
.contentBox .picBox img{
    display: block;
    width: 600px;
    margin: 0 auto;
}
.sblj{
    display: block;
    height: 60px;
    line-height: 60px;
    background: url(../images/sblj.png) no-repeat left center;
    padding-left: 81px;
}
.zcyj{
    padding-left: 34px;
    background: url(../images/wntjIcon.png) no-repeat left 3px;
    padding-bottom: 13px;
    border-bottom: 1px dashed #eee;
    margin-top: 13px;
    font-size: 16px;
    color: #333;
}
.zcyj:hover{
    color: #0052d9;
}
.contentBox .projectList li{
    height: 60px;
    line-height: 60px;
    box-sizing: border-box;
    border-bottom: 1px dashed #eee;
    font-size: 16px;
    padding-left: 26px;
    background: url(../images/wjIcon.png) no-repeat left center;
}
.contentBox .projectList li a{
    width: 699px;
}
.contentBox .projectList li a:hover{
    color: #1569e6;
}

.contentBox .projectList  span{
    color: #999;
}
.contentBox .projectList .linka .tab01{
    border: 1px solid #ffd191;
    background: #fff6e9;
    color: #ff9548;
}
.contentBox .projectList .linka .tab02{
    border: 1px solid #8ab4f2;
    background: #e7f0fc;
    color: #1569e6;
}
.contentBox .projectList .linka .tab03{
    border: 1px solid #c5c9d0;
    background: #eaebec;
    color: #8b94a1;
}
.contentBox .projectList .linka a{
    width: 627px;
    line-height: 24px;
    height: 24px;
    font-size: 16px;
}
.contentBox .projectList .infop{
    color: #8b94a1;
    line-height: 24px;
    height: 48px;
    overflow: hidden;
    margin: 10px 0 18px;
}
.contentBox .projectList .time{
    padding-left: 20px;
    background: url(.././images/icons/time.png) no-repeat left center;
    color: #8b94a1;
}
.contentBox .projectList .money{
    font-size: 18px;
    color:#ffa424;
    text-align: center;
    margin: 15px 0 10px;
}
.color1{
    color: #ff6000;
    font-weight: bold;
}
.contentBox .projectList .tip{
    color: #8b94a1;
    text-align: center;
}
.contentBox .projectList .rightli{
    width: 114px;
    border-left: 1px solid #d8e1ee;
    height: 80px;
    margin-top: 20px;
}
.zczt {
    display: inline-block;
    padding: 5px 10px;
    background-color: #fff;
    border-radius: 0px 15px 15px 15px;
    color: #0052d9 !important;
    margin-right: 10px;
}
/* 详情音频 */
#player1{
    width: 360px;
    height: 60px;
    border-radius: 50px;
    box-shadow: 0  5px 10px #d8d8d8;
}
.aplayer .aplayer-info .aplayer-music,
.aplayer .aplayer-pic img,
.aplayer .aplayer-icon-play:before,
.aplayer .aplayer-icon-pause:before,
.aplayer .aplayer-info .aplayer-controller .aplayer-volume-wrap{
    display: none !important;
}
.aplayer .aplayer-pic{
    width: 45px;
    height: 60px;
}
.aplayer .aplayer-info{
    padding: 30px 16px 0 10px;
    margin-left: 43px;
}
.aplayer .aplayer-pic .aplayer-play{
    border: none;
    width: 30px;
    height: 30px;
    background: url(.././images/icons/play.png) no-repeat;
    margin: -15px 0 0 -9px;
}
.aplayer .aplayer-pic .aplayer-pause{
    border: none;
    width: 30px;
    height: 30px;
    background: url(.././images/icons/suspend.png) no-repeat;
    top: 50%;
    left: 50%;
    margin: -15px 0 0 -9px;
}
.aplayer .aplayer-info .aplayer-controller .aplayer-time{
    font-size: 14px;
}
/* 右侧 */
.rightBox{
    width: 320px;
}
.enter01 {
    display: block;
    width: 320px;
    height: 120px;
    background: url(../images/calDEnter01.png) no-repeat;
    overflow: hidden;
    border-radius: 8px;
}

.enter02 {
    display: block;
    width: 320px;
    height: 120px;
    background: url(../images/fileD2.png) no-repeat;
    overflow: hidden;
    margin-top: 20px;
    border-radius: 8px;
}

.enterTitle {
    display: block;
    font-size: 16px;
    color: #333;
    font-weight: bold;
    margin: 30px 0 5px 34px;
}

.entertext {
    display: block;
    line-height: 18px;
    color: #999;
    margin: 20px 0 0 34px;
}

/* 推荐列表 */
.recommend {
    width: 320px;
    padding: 0px 20px;
    margin-top: 10px;
    background: url(../images/wntj.png) no-repeat left top;
    box-shadow: 0px 0px 10px rgba(0, 82, 217, .14);
    border-radius: 8px;
    box-sizing: border-box;
    padding-bottom: 20px;
    overflow: hidden;
}

.titletop {
    padding-left: 10px;
    background: url(.././images/icons/pageTitBg.png) no-repeat 5px 23px;
    height: 78px;
    line-height: 79px;
    font-size: 24px;
    font-weight: bold;
    margin-right: 20px;
    padding-top: 5px;
}

.recommend ul li {
    padding-top: 20px;
    padding-left: 34px;
    background: url(../images/wntjIcon.png) no-repeat left 20px;
}

.recommend ul li a {
    display: block;
    width: 100%;
    line-height: 24px;
    font-size: 16px;
    color: #333;
}

.recommend ul li a:hover {
    color: #1569e6;
}

.borderB {
    display: block;
    padding-bottom: 20px;
    border-bottom: 1px dashed #eaf2ff;

}

.zczt {
    display: inline-block;
    padding: 5px 10px;
    background-color: #fff;
    border-radius: 0px 15px 15px 15px;
    color: #0052d9 !important;
    margin-right: 10px;
}
/* 每日一听 */
.title .playerBox {
	cursor: move;
}
.grid-music-container,
.grid-music-container .m-music-play-wrap,
.grid-music-container .m-now-info{
    width: 100%;
    height: 100%;
    padding: 0;
    overflow: hidden;
}
.grid-music-container .m-now-info .playerTitle {
	display: block;
    width:100%;
    margin: 0 auto;
    text-align: center;
    font-weight: bold;
    height: 16px !important;
    overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	word-break: break-all;
}
.m-now-info .play_buttons{
    margin: 20px auto 0;
    width: 280px;
}
.grid-music-container .m-play-controls{
    margin: 0 auto;
}
.grid-music-container .m-now-controls .u-time{
    display: block;
    margin: 8px 0 5px 5px;
}
.grid-music-container .m-now-controls .u-process{
    display: block;
    width: 100%;
    margin: 0;
}
.box {
	position: static;
}

.box .title {
	width: 320px;
	text-align: center;
	position: absolute;
	top: -130px;
	padding-bottom: 10px;
}

.title .playerBox {
	display: block;
	width: 138px;
	height: 138px;
	border-radius: 50%;
	margin: 0 auto;
	overflow: hidden;
	background: url(../images/player.png);
	-webkit-animation: move 2500ms linear infinite;
	-moz-animation: move 2500ms linear infinite;
	-ms-animation: move 2500ms linear infinite;
	animation: move 2500ms linear infinite;
}

@-webkit-keyframes move {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@-moz-keyframes move {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@-ms-keyframes move {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@-o-keyframes move {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

@keyframes move {
	0% {
		transform: rotate(0deg);
	}

	100% {
		transform: rotate(360deg);
	}
}

.title .headset {
	width: 38px;
	height: 33px;
	left: 50%;
	margin-left: -19px;
	top: 50px;
	z-index: 1000;
	background: url(../images/headset.png) no-repeat;
}

.title .playerBox img {
	width: 100%;
}

.box .con {
	width: 280px;
	height: 140px;
	background: url(../images/bg.jpg) no-repeat;
}

.projectList li .icon01{
    background: url(.././images/icons/time.png) no-repeat left center;
    color: #8b94a1;
    padding-left: 20px;
    margin-right: 30px;
    padding-right: 0;
}
.projectList li .icon02{
    background: url(.././images/icons/view.png) no-repeat left center;
    color: #8b94a1;
    padding-left: 20px;
    margin-right: 30px;
}
.projectList li .icon03{
    background: url(.././images/icons/item.png) no-repeat left center;
    color: #8b94a1;
    padding-left: 20px;
}

.codeBox img{
    display: block;
    margin: 5px auto;
    width: 120px;
    height: 120px;
}

.tableCont table{
    width: 100%;
    margin: 20px auto 0;
}
.tableCont table tr td{
    border: 1px solid #eee;
    line-height: 24px;
    padding: 12px 10px;
    /* width: 329px; */
}
.tableCont table tr th{
    background: #f4f7fc;
    text-align: center;
    border: 1px solid #eee;
    line-height: 24px;
    padding: 12px 0px;
    /* width: 98px; */
}
.sbBtnAct{
    display: block;
    width: 146px;
    height: 46px;
    line-height: 46px;
    text-align: center;
    color: #fff;
    font-size: 18px;
    background: #0052d9;
    border-radius: 23px;
    margin: 10px 0;
    transition: all 0.3s ease;
    -webkit-transition: all 0.3s ease;
    -moz-transition: all 0.3s ease;
    -ms-transition: all 0.3s ease;
    -o-transition: all 0.3s ease;
}

.sbBtnAct:hover{
    background: #0041b3;
    transform: translateY(-2px);
    -webkit-transform: translateY(-2px);
    -moz-transform: translateY(-2px);
    -ms-transform: translateY(-2px);
    -o-transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 82, 217, 0.3);
}
/* 新版 */
.unloginTargetRead{
    width: 100%;
    height: 110px;
    background: url(../images/unloginTargetReadBg.png) no-repeat center;
    overflow: hidden;
}
.unloginTargetRead p{
    text-align: center;
    color: #666;
    font-size: 15px;
    height: 13px;
    line-height: 13px;
    margin: 20px 0 13px;
}
.unloginTargetRead a{
    display: block;
    width: 260px;
    height: 44px;
    line-height: 44px;
    color: #fff;
    text-align: center;
    border-radius: 44px;
    background-color: #1c6de2;
    margin: 0 auto;
}
.bdBox .companyName{
    padding-left: 55px;
    line-height: 37px;
    font-weight: bold;
    font-size: 18px;
    color: #333;
    background: url(../images/companyName.png) no-repeat left center;
    margin-right: 30px;
}
.bdBox .star,
.bdBox .rewardMoney{
    line-height: 37px;
    margin-right: 30px;
}
.rewardMoney em{
    color: #ff6000 ;
    font-size: 16px;
    font-weight: bold;
}
.bdBox .star em{
    width: 120px;
    height: 20px;
    display: inline-block;
    vertical-align: middle;
}
.start0{
    background: url(../images/star0.png) no-repeat;
}
.start1{
    background: url(../images/star1.png) no-repeat;
}
.start2{
    background: url(../images/star2.png) no-repeat;
}
.start3{
    background: url(../images/star3.png) no-repeat;
}
.start4{
    background: url(../images/star4.png) no-repeat;
}
.start5{
    background: url(../images/star5.png) no-repeat;
}
.bdBox .sonTitle{
    color: #1c6de2;
    margin: 25px 0 15px;
    font-size: 15px;
    line-height: 15px;
    font-weight: 700;
}
.indexsDec li{
    height: 52px;
    background-color: #f3f8fc;
    padding-top: 18px;
    margin-bottom: 10px;
}
.indexsDec li .indexName{
    color: #333;
    height: 15px;
    line-height: 15px;
    padding-left: 18px;
    border-left: 4px solid #1c6de2;
    margin: 0 0 13px;
}
.indexsDec li .indexDec{
    height: 13px;
    line-height: 13px;
    color: #999;
    padding-left: 22px;
}
.indexsDec li img{
    width: 24px;
    height: 24px;
    margin-top: 10px;
}
.editIndicators{
    padding-left: 20px;
    background: url(../images/bianji.png) no-repeat center left;
    color: #1c6de2;
    margin-top: 9px;
    margin-left: 11px;
}
/* 弹窗 */

.alertBg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 13;
}

.alertCont {
    width: 700px;
    background-color: #ffffff;
    margin: 5% auto 0;
    border-radius: 5px;
    /* overflow: hidden; */
}

.alertTitle {
    line-height: 67px;
    color: #ffffff;
    font-size: 20px;
    font-weight: bold;
    background: url(../../place/image/new_cycdPop1.png) no-repeat;
    text-align: center;
    position: relative;
}

.alertClose {
    width: 15px;
    height: 15px;
    position: absolute;
    right: 20px;
    top: 26px;
}

.alertClose:hover {
    cursor: pointer;
}

.alertClose img {
    display: block;
    width: 100%;
}
.alertUl {
    max-height: 560px;
    padding: 30px ;
    background: url(../../place/image/new_cycdPop2.png) no-repeat bottom center;
}
.formUnit .titleP{
    line-height: 40px;
}
.formUnit  select{
    width: 100%;
    border: 1px solid #ddd;
    height: 42px;
    border-radius: 8px;
}
.formUnit  select.areas{
    width: 30%;
    margin-right: 3%;
}
.formUnit  select.industry{
    width: 46%;
    margin-right: 3%;
}
.formUnit input[type='text'],
.formUnit input[type='number']{
    width: 80%;
    border: 1px solid #ddd;
    height: 42px;
    border-radius: 8px;
    padding: 0 10px;
}
.alertUl .btnCancel {
    width: 110px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    border: 1px solid #8db6f2;
    color: #1569e6;
    background-color: #e7f1fc;
    border-radius: 50px;
}
.alertUl .btnSubmit{
    width: 110px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    font-size: 14px;
    background: url(../../place/image/new_cycdPop3.png) no-repeat;
    color: #ffffff;
    border-radius: 50px;
}
.btnstyle{
    width: 250px;
    margin: 30px auto 0;
}
.approvedEnterprisesList{
    width: 100%;
    margin: 20px auto ;
}
.approvedEnterprisesList td{
    border: 1px solid #eee;
    padding: 5px 10px;
}
.approvedEnterprisesList th{
    border: 1px solid #eee;
    text-align: center;
    font-weight: bold;
    background: #f6f9ff;
    padding: 10px 0;
}
.qymd{
    display: none;
}

/* 底部样式 */
.footerBar{
    height: 215px;
    width: 100%;
    min-width: 1400px;
    background: #0052d9;
    background-size: 100% 100%;
    margin-top: 40px;
}

.footerMain{
    width: 1400px;
    min-width: 1400px;
    margin: 0 auto;
    padding-top: 20px;
}

.footerMain .linkBox{
    line-height: 56px;
    height: 56px;
    border-bottom: 1px dashed rgba(255, 255, 255, 0.3);
}

.footerMain .linkBox a{
    color: #fff;
    font-size: 16px;
    text-decoration: none;
    transition: color 0.3s;
}

.footerMain .linkBox a:hover{
    color: #ff8a00;
}

.footerMain .linkBox em{
    color: #fff;
    padding: 0 15px;
    font-style: normal;
}

.footerMain .linkBox select{
    background: none;
    border: 1px solid rgba(255, 255, 255, 0.3);
    color: #fff;
    font-size: 16px;
    padding: 5px 10px;
    border-radius: 4px;
}

.footerMain .linkBox select option{
    color: #333;
    background: #fff;
}

.footerMain .bottomBoxfoot{
    line-height: 28px;
    color: rgba(255, 255, 255, 0.8);
    margin-top: 20px;
}

.footerMain .bottomBoxfoot .textBox{
    width: 60%;
}

.footerMain .bottomBoxfoot .textBox a{
    color: rgba(255, 255, 255, 0.8);
    margin-right: 25px;
    text-decoration: none;
}

.footerMain .bottomBoxfoot .textBox a:hover{
    color: #fff;
}

.footerMain .codeBox{
    width: 180px;
    height: 100px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-top: 10px;
    text-align: center;
    padding: 10px;
}

.footerMain .codeBox img{
    width: 80px;
    height: 80px;
    border-radius: 4px;
}

.footerMain .codeBox p{
    color: #fff;
    font-size: 14px;
    margin-top: 5px;
    line-height: 18px;
}